"""
Libreria CAM per la generazione di percorsi di lavorazione CNC.

Moduli principali:
- blank: Gestione grezzi
- coordinate_system: Sistemi di coordinate
- operations: Lavorazioni (sgrossatura, contornatura, incisione)
- path_generator: Generazione percorsi
- optimization: Ottimizzazione percorsi
- setup: Configurazione setup di lavorazione
- tool_lib: Libreria utensili
- gcode_generator: Generazione G-code
"""

from .blank import Blank, CylindricalBlank, RectangularBlank, ModelBasedBlank
from .coordinate_system import CoordinateSystem, AxisConfig, AxisDirection
from .operations import Operation, OperationType, RoughingOperation, ContouringOperation, EngravingOperation
from .path_generator import PathGenerator
from .optimization import PathOptimizer, OptimizationSettings
from .setup import Setup, CAMSetup
from .gcode_generator import GCodeGenerator

__version__ = "1.0.0"
__all__ = [
    "Blank", "CylindricalBlank", "RectangularBlank", "ModelBasedBlank",
    "CoordinateSystem", "AxisConfig", "AxisDirection",
    "Operation", "OperationType", "RoughingOperation", "ContouringOperation", "EngravingOperation",
    "PathGenerator", "PathOptimizer", "OptimizationSettings",
    "Setup", "CAMSetup", "GCodeGenerator"
]
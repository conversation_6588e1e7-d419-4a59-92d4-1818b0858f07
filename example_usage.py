"""
Esempio di utilizzo della libreria CAM riadattata.

Questo esempio mostra come:
1. <PERSON><PERSON><PERSON> un grezzo
2. Configurare il sistema di coordinate
3. <PERSON><PERSON>re lavorazioni (sgrossatura, contornatura, incisione)
4. Generar<PERSON> e ottimizzare i percorsi
5. Esportare il G-code

Utilizza solo matematica pura senza dipendenze da librerie 3D.
"""

# Import della libreria CAM
from blank import CylindricalBlank, RectangularBlank, create_blank_from_bbox, GeometricShape
from coordinate_system import CoordinateSystem
from operations import OperationType, MathematicalSurface
from setup import CAMSetup
from optimization import OptimizationSettings

# Prova a importare get_recommended_parameters
try:
    from operations import get_recommended_parameters
except ImportError:
    def get_recommended_parameters(operation_type, material, tool_diameter):
        return {"spindle_speed": 2000, "feed_rate": 200, "plunge_rate": 50}

# Import tool_lib con gestione errori
try:
    from tool_lib.tool_lib.milling import EndMill
    from tool_lib.tool_lib.drilling import DrillBit
except ImportError:
    # Classi placeholder se tool_lib non disponibile
    class EndMill:
        def __init__(self, name, diameter, length, flutes=2, mill_type="flat", code=None):
            self.name = name
            self.diameter = diameter
            self.length = length
            self.flutes = flutes
            self.mill_type = mill_type
            self.code = code

    class DrillBit:
        def __init__(self, name, diameter, length, code=None):
            self.name = name
            self.diameter = diameter
            self.length = length
            self.code = code


def example_cylindrical_engraving():
    """Esempio di incisione su cilindro."""
    print("=== Esempio: Incisione su Cilindro ===")

    # 1. Crea il setup
    setup = CAMSetup("Incisione_Cilindro")

    # 2. Crea il grezzo cilindrico
    blank = CylindricalBlank(
        name="Cilindro_Alluminio",
        diameter=30.0,  # mm
        height=50.0,    # mm
        center=(0, 0, 0),
        material="Aluminum"
    )
    setup.set_blank(blank)

    # 3. Configura il sistema di coordinate per lavorazione cilindrica
    coord_system = CoordinateSystem("Fresatura_Cilindrica")
    coord_system.set_origin(0, 0, 0)  # Origine al centro del cilindro
    setup.set_coordinate_system(coord_system, work_offset_number=1)  # G54

    # 4. Crea utensile per incisione
    engraving_tool = EndMill(
        name="Fresa_Incisione_1mm",
        diameter=1.0,
        length=50.0,
        flutes=2,
        mill_type="flat",
        code="T1"
    )

    # 5. Crea superficie cilindrica per incisione
    cylinder_surface = MathematicalSurface('cylinder', {
        'center': (0, 0, 0),
        'radius': blank.radius,
        'height': blank.height
    })

    # 6. Crea lavorazione di incisione
    engraving_op = setup.create_operation(
        operation_type=OperationType.ENGRAVING,
        name="Incisione_Testo",
        tool=engraving_tool,
        surfaces=[cylinder_surface]
    )

    # Configura parametri incisione
    engraving_op.set_engraving_parameters(
        depth=0.2,      # mm profondità incisione
        text="CAM LIB", # Testo da incidere
        font_size=8.0   # mm altezza caratteri
    )

    # Parametri raccomandati per alluminio
    recommended = get_recommended_parameters(OperationType.ENGRAVING, "Aluminum", 1.0)
    engraving_op.set_speeds_and_feeds(
        spindle_speed=recommended["spindle_speed"],
        feed_rate=recommended["feed_rate"],
        plunge_rate=25
    )

    engraving_op.set_heights(safe_height=10.0, clearance_height=2.0)
    engraving_op.set_precision(tolerance=0.01)

    # 6. Valida e genera percorsi
    is_valid, errors = setup.validate_setup()
    if not is_valid:
        print(f"Setup non valido: {errors}")
        return

    print("Generazione percorsi...")
    tool_paths = setup.generate_all_paths(enable_optimization=True)

    # 7. Mostra risultati
    setup_info = setup.get_setup_info()
    print(f"Setup: {setup_info['name']}")
    print(f"Grezzo: {setup_info['blank']['name']} ({setup_info['blank']['material']})")
    print(f"Operazioni: {len(setup_info['operations'])}")
    print(f"Tempo stimato: {setup_info['total_machining_time']:.2f} minuti")

    # 8. Esporta G-code
    gcode_file = "incisione_cilindro.nc"
    if setup.export_gcode(gcode_file):
        print(f"G-code esportato in: {gcode_file}")

    return setup


def example_rectangular_roughing_finishing():
    """Esempio di sgrossatura e finitura su pezzo rettangolare."""
    print("\n=== Esempio: Sgrossatura e Finitura Rettangolare ===")

    # 1. Crea il setup
    setup = CAMSetup("Sgrossatura_Finitura")

    # 2. Crea superfici del modello da lavorare
    model_surface = MathematicalSurface('plane', {
        'center': (25, 15, 20),
        'size': (50, 30),
        'normal': (0, 0, 1)
    })
    setup.add_model_surface(model_surface)

    # 3. Crea grezzo rettangolare con offset
    blank = RectangularBlank(
        name="Grezzo_Acciaio",
        length=60.0,    # mm (con 5mm di offset per lato)
        width=40.0,     # mm
        height=25.0,    # mm
        corner=(-5, -5, 0),  # Offset di 5mm
        material="Steel"
    )
    setup.set_blank(blank)

    # 4. Sistema di coordinate standard per fresatura
    coord_system = CoordinateSystem("Fresatura_Standard")
    coord_system.set_origin(25, 15, 20)  # Origine al centro della superficie superiore
    setup.set_coordinate_system(coord_system, work_offset_number=1)

    # 6. Utensili
    roughing_tool = EndMill(
        name="Fresa_Sgrossatura_8mm",
        diameter=8.0,
        length=60.0,
        flutes=3,
        mill_type="flat",
        code="T1"
    )

    finishing_tool = EndMill(
        name="Fresa_Finitura_4mm",
        diameter=4.0,
        length=50.0,
        flutes=4,
        mill_type="flat",
        code="T2"
    )

    # 5. Lavorazione di sgrossatura
    roughing_op = setup.create_operation(
        operation_type=OperationType.ROUGHING,
        name="Sgrossatura",
        tool=roughing_tool,
        surfaces=[model_surface]
    )

    # Parametri sgrossatura
    roughing_op.set_roughing_parameters(
        stock_to_leave=0.5,    # mm sovrametallo
        max_depth=2.0,         # mm per passata
        strategy="zigzag"
    )

    recommended_rough = get_recommended_parameters(OperationType.ROUGHING, "Steel", 8.0)
    roughing_op.set_speeds_and_feeds(
        spindle_speed=recommended_rough["spindle_speed"],
        feed_rate=recommended_rough["feed_rate"],
        plunge_rate=50
    )

    roughing_op.set_precision(tolerance=0.1, step_down=2.0, step_over=6.0)
    roughing_op.set_heights(safe_height=15.0, clearance_height=3.0)

    # 6. Lavorazione di finitura
    finishing_op = setup.create_operation(
        operation_type=OperationType.CONTOURING,
        name="Finitura",
        tool=finishing_tool,
        surfaces=[model_surface]
    )

    # Parametri finitura
    finishing_op.set_contouring_parameters(
        finish_passes=2,
        spring_passes=1,
        lead_in=3.0,
        lead_out=3.0
    )

    recommended_finish = get_recommended_parameters(OperationType.CONTOURING, "Steel", 4.0)
    finishing_op.set_speeds_and_feeds(
        spindle_speed=recommended_finish["spindle_speed"],
        feed_rate=recommended_finish["feed_rate"],
        plunge_rate=30
    )

    finishing_op.set_precision(tolerance=0.02, step_down=0.5)
    finishing_op.set_heights(safe_height=15.0, clearance_height=2.0)

    # 9. Configura ottimizzazione avanzata
    setup.optimization_settings.enable_arc_fitting = True
    setup.optimization_settings.enable_point_reduction = True
    setup.optimization_settings.chord_tolerance = 0.005

    # 10. Genera percorsi
    is_valid, errors = setup.validate_setup()
    if not is_valid:
        print(f"Setup non valido: {errors}")
        return

    print("Generazione percorsi con ottimizzazione...")
    tool_paths = setup.generate_all_paths(enable_optimization=True)

    # 11. Analisi risultati
    setup_info = setup.get_setup_info()
    print(f"Setup: {setup_info['name']}")
    print(f"Operazioni completate: {len(tool_paths)}")

    for i, path in enumerate(tool_paths):
        print(f"  Operazione {i+1}: {path.operation.name}")
        print(f"    Utensile: {path.tool.name}")
        print(f"    Punti percorso: {len(path.points)}")
        print(f"    Lunghezza: {path.total_length:.2f} mm")
        print(f"    Tempo: {path.machining_time:.2f} min")

    print(f"Tempo totale stimato: {setup_info['total_machining_time']:.2f} minuti")

    # 12. Esporta G-code
    gcode_file = "sgrossatura_finitura.nc"
    if setup.export_gcode(gcode_file):
        print(f"G-code esportato in: {gcode_file}")

    return setup


def example_automatic_blank_creation():
    """Esempio di creazione automatica del grezzo da bounding box."""
    print("\n=== Esempio: Creazione Automatica Grezzo ===")

    # 1. Definisce il bounding box di un modello cilindrico
    # Cilindro con raggio 15mm e altezza 40mm
    cylinder_bbox = ((-15, -15, 0), (15, 15, 40))

    # 2. Crea automaticamente diversi tipi di grezzo
    from blank import create_blank_from_bbox

    cylindrical_blank = create_blank_from_bbox(
        cylinder_bbox,
        blank_type="cylindrical",
        offset=3.0,
        material="Aluminum"
    )

    rectangular_blank = create_blank_from_bbox(
        cylinder_bbox,
        blank_type="rectangular",
        offset=5.0,
        material="Steel"
    )

    model_based_blank = create_blank_from_bbox(
        cylinder_bbox,
        blank_type="model_based",
        offset=2.0,
        material="Plastic"
    )

    print("Grezzi creati automaticamente:")
    print(f"1. Cilindrico: {cylindrical_blank.name} - Ø{cylindrical_blank.diameter}x{cylindrical_blank.height}mm")
    print(f"2. Rettangolare: {rectangular_blank.name} - {rectangular_blank.length}x{rectangular_blank.width}x{rectangular_blank.height}mm")
    print(f"3. Basato su modello: {model_based_blank.name}")

    # Calcola volumi
    for blank in [cylindrical_blank, rectangular_blank, model_based_blank]:
        volume = blank.get_volume()
        bbox = blank.get_bounding_box()
        print(f"   Volume: {volume:.2f} mm³, BBox: {bbox}")


def main():
    """Esegue tutti gli esempi."""
    print("Esempi di utilizzo della Libreria CAM")
    print("=" * 50)

    try:
        # Esempio 1: Incisione cilindrica
        setup1 = example_cylindrical_engraving()

        # Esempio 2: Sgrossatura e finitura
        setup2 = example_rectangular_roughing_finishing()

        # Esempio 3: Creazione automatica grezzi
        example_automatic_blank_creation()

        print("\n" + "=" * 50)
        print("Tutti gli esempi completati con successo!")

    except Exception as e:
        print(f"Errore durante l'esecuzione degli esempi: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

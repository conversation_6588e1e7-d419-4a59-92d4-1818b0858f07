"""
Modulo per la gestione dei grezzi (blank/stock) per lavorazioni CAM.

Supporta diversi tipi di grezzi:
- Cilindrico
- Rettangolare/Quadrato
- Basato su modello matematico
- Con offset personalizzabili

Utilizza solo matematica pura senza dipendenze da librerie 3D.
"""

from abc import ABC, abstractmethod
from typing import Tuple, Optional, Union, List, Dict, Any
import math


class GeometricShape:
    """Rappresentazione matematica di una forma geometrica."""

    def __init__(self, shape_type: str, parameters: Dict[str, Any]):
        """
        Inizializza una forma geometrica.

        Args:
            shape_type: Tipo di forma ('box', 'cylinder', 'custom')
            parameters: Parametri specifici della forma
        """
        self.shape_type = shape_type
        self.parameters = parameters

    def get_bounding_box(self) -> <PERSON><PERSON>[Tuple[float, float, float], Tu<PERSON>[float, float, float]]:
        """Calcola il bounding box della forma."""
        if self.shape_type == 'box':
            corner = self.parameters.get('corner', (0, 0, 0))
            length = self.parameters.get('length', 0)
            width = self.parameters.get('width', 0)
            height = self.parameters.get('height', 0)

            min_point = corner
            max_point = (corner[0] + length, corner[1] + width, corner[2] + height)
            return (min_point, max_point)

        elif self.shape_type == 'cylinder':
            center = self.parameters.get('center', (0, 0, 0))
            radius = self.parameters.get('radius', 0)
            height = self.parameters.get('height', 0)

            min_point = (center[0] - radius, center[1] - radius, center[2])
            max_point = (center[0] + radius, center[1] + radius, center[2] + height)
            return (min_point, max_point)

        else:
            # Forma custom - usa bbox fornito
            return self.parameters.get('bbox', ((0, 0, 0), (1, 1, 1)))

    def get_volume(self) -> float:
        """Calcola il volume della forma."""
        if self.shape_type == 'box':
            length = self.parameters.get('length', 0)
            width = self.parameters.get('width', 0)
            height = self.parameters.get('height', 0)
            return length * width * height

        elif self.shape_type == 'cylinder':
            radius = self.parameters.get('radius', 0)
            height = self.parameters.get('height', 0)
            return math.pi * radius * radius * height

        else:
            # Forma custom - usa volume fornito o calcola da bbox
            if 'volume' in self.parameters:
                return self.parameters['volume']
            else:
                bbox = self.get_bounding_box()
                dims = (bbox[1][0] - bbox[0][0], bbox[1][1] - bbox[0][1], bbox[1][2] - bbox[0][2])
                return dims[0] * dims[1] * dims[2]

    def contains_point(self, point: Tuple[float, float, float]) -> bool:
        """Verifica se un punto è contenuto nella forma."""
        x, y, z = point

        if self.shape_type == 'box':
            corner = self.parameters.get('corner', (0, 0, 0))
            length = self.parameters.get('length', 0)
            width = self.parameters.get('width', 0)
            height = self.parameters.get('height', 0)

            return (corner[0] <= x <= corner[0] + length and
                    corner[1] <= y <= corner[1] + width and
                    corner[2] <= z <= corner[2] + height)

        elif self.shape_type == 'cylinder':
            center = self.parameters.get('center', (0, 0, 0))
            radius = self.parameters.get('radius', 0)
            height = self.parameters.get('height', 0)

            # Distanza dal centro nel piano XY
            dx = x - center[0]
            dy = y - center[1]
            distance_xy = math.sqrt(dx*dx + dy*dy)

            return (distance_xy <= radius and
                    center[2] <= z <= center[2] + height)

        return False


class Blank(ABC):
    """Classe base astratta per tutti i tipi di grezzi."""

    def __init__(self, name: str, material: str = "Aluminum"):
        """
        Inizializza il grezzo base.

        Args:
            name: Nome identificativo del grezzo
            material: Materiale del grezzo
        """
        self.name = name
        self.material = material
        self.offset_x = 0.0
        self.offset_y = 0.0
        self.offset_z = 0.0
        self._shape = None

    @abstractmethod
    def create_shape(self) -> GeometricShape:
        """Crea la forma geometrica del grezzo."""
        pass

    def set_offset(self, x: float = 0.0, y: float = 0.0, z: float = 0.0):
        """
        Imposta gli offset dal modello da lavorare.

        Args:
            x, y, z: Offset nelle tre direzioni in mm
        """
        self.offset_x = x
        self.offset_y = y
        self.offset_z = z
        self._shape = None  # Forza la rigenerazione

    def get_shape(self) -> GeometricShape:
        """Restituisce la forma del grezzo."""
        if self._shape is None:
            self._shape = self.create_shape()
        return self._shape

    def get_bounding_box(self) -> Tuple[Tuple[float, float, float], Tuple[float, float, float]]:
        """
        Calcola il bounding box del grezzo.

        Returns:
            Tuple con punto minimo e massimo del bounding box
        """
        return self.get_shape().get_bounding_box()

    def get_volume(self) -> float:
        """Calcola il volume del grezzo."""
        return self.get_shape().get_volume()

    def contains_point(self, point: Tuple[float, float, float]) -> bool:
        """Verifica se un punto è contenuto nel grezzo."""
        return self.get_shape().contains_point(point)


class CylindricalBlank(Blank):
    """Grezzo cilindrico."""

    def __init__(self, name: str, diameter: float, height: float,
                 center: Tuple[float, float, float] = (0, 0, 0),
                 axis_direction: Tuple[float, float, float] = (0, 0, 1),
                 material: str = "Aluminum"):
        """
        Inizializza un grezzo cilindrico.

        Args:
            name: Nome del grezzo
            diameter: Diametro del cilindro in mm
            height: Altezza del cilindro in mm
            center: Centro del cilindro (x, y, z)
            axis_direction: Direzione dell'asse del cilindro
            material: Materiale
        """
        super().__init__(name, material)
        self.diameter = diameter
        self.radius = diameter / 2.0
        self.height = height
        self.center = center
        self.axis_direction = axis_direction

    def create_shape(self) -> GeometricShape:
        """Crea la forma cilindrica."""
        # Applica gli offset al centro
        adjusted_center = (
            self.center[0] + self.offset_x,
            self.center[1] + self.offset_y,
            self.center[2] + self.offset_z
        )

        parameters = {
            'center': adjusted_center,
            'radius': self.radius,
            'height': self.height,
            'axis_direction': self.axis_direction
        }

        return GeometricShape('cylinder', parameters)

    def set_diameter(self, diameter: float):
        """Modifica il diametro del cilindro."""
        self.diameter = diameter
        self.radius = diameter / 2.0
        self._shape = None  # Forza la rigenerazione

    def set_height(self, height: float):
        """Modifica l'altezza del cilindro."""
        self.height = height
        self._shape = None  # Forza la rigenerazione


class RectangularBlank(Blank):
    """Grezzo rettangolare/quadrato."""

    def __init__(self, name: str, length: float, width: float, height: float,
                 corner: Tuple[float, float, float] = (0, 0, 0),
                 material: str = "Aluminum"):
        """
        Inizializza un grezzo rettangolare.

        Args:
            name: Nome del grezzo
            length: Lunghezza in mm (direzione X)
            width: Larghezza in mm (direzione Y)
            height: Altezza in mm (direzione Z)
            corner: Punto dell'angolo inferiore (x, y, z)
            material: Materiale
        """
        super().__init__(name, material)
        self.length = length
        self.width = width
        self.height = height
        self.corner = corner

    def create_shape(self) -> GeometricShape:
        """Crea la forma rettangolare."""
        # Applica gli offset al corner
        adjusted_corner = (
            self.corner[0] + self.offset_x,
            self.corner[1] + self.offset_y,
            self.corner[2] + self.offset_z
        )

        parameters = {
            'corner': adjusted_corner,
            'length': self.length,
            'width': self.width,
            'height': self.height
        }

        return GeometricShape('box', parameters)

    def set_dimensions(self, length: float, width: float, height: float):
        """Modifica le dimensioni del grezzo."""
        self.length = length
        self.width = width
        self.height = height
        self._shape = None  # Forza la rigenerazione

    @classmethod
    def create_square(cls, name: str, side: float, height: float,
                     corner: Tuple[float, float, float] = (0, 0, 0),
                     material: str = "Aluminum"):
        """
        Crea un grezzo quadrato.

        Args:
            name: Nome del grezzo
            side: Lato del quadrato in mm
            height: Altezza in mm
            corner: Punto dell'angolo inferiore
            material: Materiale
        """
        return cls(name, side, side, height, corner, material)


class ModelBasedBlank(Blank):
    """Grezzo basato su un modello matematico esistente."""

    def __init__(self, name: str, base_shape: GeometricShape,
                 uniform_offset: float = 0.0,
                 offset_x: float = 0.0, offset_y: float = 0.0, offset_z: float = 0.0,
                 material: str = "Aluminum"):
        """
        Inizializza un grezzo basato su modello.

        Args:
            name: Nome del grezzo
            base_shape: Forma base da cui derivare il grezzo
            uniform_offset: Offset uniforme in tutte le direzioni
            offset_x, offset_y, offset_z: Offset direzionali
            material: Materiale
        """
        super().__init__(name, material)
        self.base_shape = base_shape
        self.uniform_offset = uniform_offset
        self.set_offset(offset_x, offset_y, offset_z)

    def create_shape(self) -> GeometricShape:
        """Crea la forma basata sul modello con offset."""
        if self.uniform_offset == 0.0 and all(o == 0.0 for o in [self.offset_x, self.offset_y, self.offset_z]):
            return self.base_shape

        # Applica gli offset al bounding box della forma base
        base_bbox = self.base_shape.get_bounding_box()
        min_point = base_bbox[0]
        max_point = base_bbox[1]

        # Espandi il bounding box con gli offset
        expanded_min = (
            min_point[0] - self.uniform_offset - self.offset_x,
            min_point[1] - self.uniform_offset - self.offset_y,
            min_point[2] - self.uniform_offset - self.offset_z
        )
        expanded_max = (
            max_point[0] + self.uniform_offset + self.offset_x,
            max_point[1] + self.uniform_offset + self.offset_y,
            max_point[2] + self.uniform_offset + self.offset_z
        )

        # Crea una forma custom con il bounding box espanso
        parameters = {
            'bbox': (expanded_min, expanded_max),
            'volume': self.base_shape.get_volume() * 1.5  # Stima approssimativa
        }

        return GeometricShape('custom', parameters)

    def set_base_shape(self, shape: GeometricShape):
        """Modifica la forma base."""
        self.base_shape = shape
        self._shape = None  # Forza la rigenerazione

    def set_uniform_offset(self, offset: float):
        """Imposta un offset uniforme."""
        self.uniform_offset = offset
        self._shape = None  # Forza la rigenerazione


def create_blank_from_bbox(bbox: Tuple[Tuple[float, float, float], Tuple[float, float, float]],
                          blank_type: str = "cylindrical",
                          offset: float = 5.0, material: str = "Aluminum") -> Blank:
    """
    Crea automaticamente un grezzo appropriato basato su un bounding box.

    Args:
        bbox: Bounding box del modello ((xmin, ymin, zmin), (xmax, ymax, zmax))
        blank_type: Tipo di grezzo ("cylindrical", "rectangular", "model_based")
        offset: Offset dal modello in mm
        material: Materiale del grezzo

    Returns:
        Istanza del grezzo appropriato
    """
    min_point, max_point = bbox
    xmin, ymin, zmin = min_point
    xmax, ymax, zmax = max_point

    length = xmax - xmin + 2 * offset
    width = ymax - ymin + 2 * offset
    height = zmax - zmin + 2 * offset

    if blank_type == "cylindrical":
        # Usa il diametro maggiore tra lunghezza e larghezza
        diameter = max(length, width)
        center = ((xmin + xmax) / 2, (ymin + ymax) / 2, zmin - offset)
        return CylindricalBlank("Auto_Cylindrical", diameter, height, center, material=material)

    elif blank_type == "rectangular":
        corner = (xmin - offset, ymin - offset, zmin - offset)
        return RectangularBlank("Auto_Rectangular", length, width, height, corner, material=material)

    elif blank_type == "model_based":
        # Crea una forma custom dal bounding box
        custom_shape = GeometricShape('custom', {'bbox': bbox})
        return ModelBasedBlank("Auto_ModelBased", custom_shape, uniform_offset=offset, material=material)

    else:
        raise ValueError(f"Tipo di grezzo non supportato: {blank_type}")


def create_blank_from_model(model_shape: GeometricShape, blank_type: str = "cylindrical",
                           offset: float = 5.0, material: str = "Aluminum") -> Blank:
    """
    Crea automaticamente un grezzo appropriato basato su un modello geometrico.

    Args:
        model_shape: Forma geometrica del modello da lavorare
        blank_type: Tipo di grezzo ("cylindrical", "rectangular", "model_based")
        offset: Offset dal modello in mm
        material: Materiale del grezzo

    Returns:
        Istanza del grezzo appropriato
    """
    bbox = model_shape.get_bounding_box()
    return create_blank_from_bbox(bbox, blank_type, offset, material)
